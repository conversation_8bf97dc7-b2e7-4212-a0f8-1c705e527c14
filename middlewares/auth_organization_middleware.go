package middlewares

import (
	"context"
	"net/http"
	"strings"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

// AuthMiddleware extracts organization and service data from API key and validates service access
func AuthMiddleware(apiKeyService *services.APIKeyService, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			auth := r.Header.Get("Authorization")
			if auth == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			token := strings.TrimPrefix(auth, "Bearer ")
			if token == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			if !apiKeyService.IsValidApiKey(token) {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			ctx := r.Context()

			orgID, dbServiceID, err := apiKeyService.GetDataByAPIKey(ctx, token)
			if err != nil {
				logger.Error("failed to get data by API key", zap.Error(err))
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			if contextServiceID, ok := GetServiceIDFromContext(ctx); ok {
				if contextServiceID.String() != dbServiceID.String() {
					logger.Warn("service ID mismatch",
						zap.String("context_service_id", contextServiceID.String()),
						zap.String("db_service_id", dbServiceID.String()))
					helpers.RenderJSONError(w, r, pkg.ErrForbidden)
					return
				}
			}

			ctx = context.WithValue(ctx, OrgIDKey{}, orgID)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
