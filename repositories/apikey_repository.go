package repositories

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type APIKeyRepositoryInterface interface {
	IsActiveAPIKey(ctx context.Context, apiKey string) bool
	GetDataByAPIKey(ctx context.Context, apiKey string) (*appioid.ID, *appioid.ID, error)
}

type APIKeyRepository struct {
	DB *pgxpool.Pool
}

func NewAPIKeyRepository(db *pgxpool.Pool) *APIKeyRepository {
	return &APIKeyRepository{
		DB: db,
	}
}

func (r *APIKeyRepository) IsActiveAPIKey(ctx context.Context, apiKey string) bool {
	query := `
		SELECT EXISTS (
			SELECT 1 FROM api_keys
			WHERE api_key=@api_key AND deactivated_at IS NULL
		)`
	args := pgx.NamedArgs{"api_key": apiKey}
	var active bool
	if err := r.DB.QueryRow(ctx, query, args).Scan(&active); err != nil {
		return false
	}
	return active
}

func (r *APIKeyRepository) GetDataByAPIKey(ctx context.Context, apiKey string) (*appioid.ID, *appioid.ID, error) {
	query := `
		SELECT organization_id, service_id FROM api_keys
		WHERE api_key=@api_key AND deactivated_at IS NULL
		`
	args := pgx.NamedArgs{"api_key": apiKey}
	var orgId appioid.ID
	var svcID sql.NullString
	err := r.DB.QueryRow(ctx, query, args).Scan(&orgId, &svcID)

	if err != nil {
		return nil, nil, fmt.Errorf("getting data by API key: %w", err)
	}

	var serviceID *appioid.ID
	if svcID.Valid {
		parsedID, err := appioid.Parse(svcID.String)
		if err != nil {
			return nil, nil, fmt.Errorf("parsing service ID: %w", err)
		}
		serviceID = &parsedID
	}

	return &orgId, serviceID, nil
}
